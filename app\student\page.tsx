"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { TrendingUp, Calendar, LogOut, Trophy, Flame, Zap, Target, BookOpen, Users, Sparkles } from "lucide-react"

// Mock student data
const studentData = {
  name: "<PERSON><PERSON> <PERSON>",
  level: 5,
  xp: 1250,
  nextLevelXP: 1500,
  totalBadges: 8,
  streak: 12,
  rank: 3,
  center: "Main Center",
  badges: [
    { name: "Perfect Attendance", earned: "2024-01-15", color: "bg-green-100 text-green-800", icon: "🎯" },
    { name: "Math Wizard", earned: "2024-01-10", color: "bg-blue-100 text-blue-800", icon: "🧮" },
    { name: "Helper Hero", earned: "2024-01-05", color: "bg-purple-100 text-purple-800", icon: "🤝" },
    { name: "Reading Star", earned: "2023-12-20", color: "bg-yellow-100 text-yellow-800", icon: "📚" },
  ],
  recentActivities: [
    { activity: "Completed math homework", xp: 25, date: "2024-01-20", emoji: "📝" },
    { activity: "Perfect attendance this week", xp: 50, date: "2024-01-19", emoji: "✅" },
    { activity: "Helped classmate with reading", xp: 30, date: "2024-01-18", emoji: "🤝" },
    { activity: "Scored 95% in science test", xp: 40, date: "2024-01-17", emoji: "🧪" },
  ],
  goals: [
    { goal: "Read 5 books this month", progress: 60, deadline: "2024-01-31", emoji: "📖" },
    { goal: "Improve handwriting", progress: 80, deadline: "2024-02-15", emoji: "✍️" },
    { goal: "Learn multiplication tables", progress: 45, deadline: "2024-02-28", emoji: "🔢" },
  ],
  weeklyProgress: {
    attendance: 100,
    homework: 85,
    behavior: 95,
    participation: 90,
  },
}

const leaderboard = [
  { rank: 1, name: "Rahul Kumar", xp: 1380, badges: 9, avatar: "🦸‍♂️" },
  { rank: 2, name: "Anita Singh", xp: 1290, badges: 7, avatar: "🌟" },
  { rank: 3, name: "Priya Sharma", xp: 1250, badges: 8, avatar: "🎨" },
  { rank: 4, name: "Vikram Patel", xp: 1180, badges: 6, avatar: "🚀" },
  { rank: 5, name: "Meera Gupta", xp: 1120, badges: 5, avatar: "🎭" },
]

const motivationalMessages = [
  "You're doing amazing! Keep it up! 🌟",
  "Every step forward is progress! 🚀",
  "You're a learning superstar! ⭐",
  "Great job on your streak! 🔥",
  "You're making your teachers proud! 👏",
]

export default function StudentDashboard() {
  const [user, setUser] = useState<any>(null)
  const [currentMessage, setCurrentMessage] = useState(0)

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      const parsedUser = JSON.parse(userData)
      if (parsedUser.role !== "student") {
        alert("Access denied. Student role required.")
        window.location.href = "/"
        return
      }
      setUser(parsedUser)
    } else {
      window.location.href = "/"
    }

    // Rotate motivational messages
    const interval = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % motivationalMessages.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  const handleLogout = () => {
    localStorage.removeItem("user")
    window.location.href = "/"
  }

  if (!user) {
    return <div>Loading...</div>
  }

  const progressToNext = (studentData.xp / studentData.nextLevelXP) * 100

  return (
    <div
      className="min-h-screen relative"
      style={{
        backgroundImage: `url('https://hebbkx1anhila5yf.public.blob.vercel-storage.com/hand-drawn-doodle-background_23-2149968652-gX16QQHWo19KpJxguAu8gbDATfSBg0.avif')`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      {/* Overlay for better readability */}
      <div className="absolute inset-0 bg-white/80 backdrop-blur-sm"></div>

      <div className="relative z-10">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 via-pink-500 to-orange-400 shadow-lg">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center justify-between">
              <div className="text-white">
                <h1 className="text-3xl font-bold flex items-center gap-2">
                  <Sparkles className="h-8 w-8" />
                  My Learning Adventure
                </h1>
                <p className="text-purple-100 text-lg">Welcome back, {studentData.name}! 🎉</p>
              </div>
              <div className="flex items-center gap-4">
                <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-lg px-4 py-2">
                  Level {studentData.level} Student
                </Badge>
                <Button
                  variant="outline"
                  onClick={handleLogout}
                  className="bg-white/20 text-white border-white/30 hover:bg-white/30"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          {/* Motivational Message */}
          <Card className="mb-8 bg-gradient-to-r from-yellow-200 to-orange-200 border-none shadow-lg">
            <CardContent className="pt-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-800 animate-pulse">
                  {motivationalMessages[currentMessage]}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Hero Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="bg-gradient-to-br from-blue-400 to-blue-600 text-white shadow-xl transform hover:scale-105 transition-transform">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-lg">Current Level</p>
                    <p className="text-4xl font-bold">{studentData.level}</p>
                    <p className="text-blue-200 text-sm">Learning Hero!</p>
                  </div>
                  <div className="text-6xl">🌟</div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-yellow-400 to-orange-500 text-white shadow-xl transform hover:scale-105 transition-transform">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-100 text-lg">Total XP</p>
                    <p className="text-4xl font-bold">{studentData.xp}</p>
                    <p className="text-yellow-200 text-sm">Experience Points</p>
                  </div>
                  <div className="text-6xl">⚡</div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-400 to-purple-600 text-white shadow-xl transform hover:scale-105 transition-transform">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-lg">Badges Earned</p>
                    <p className="text-4xl font-bold">{studentData.totalBadges}</p>
                    <p className="text-purple-200 text-sm">Achievements</p>
                  </div>
                  <div className="text-6xl">🏆</div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-red-400 to-pink-500 text-white shadow-xl transform hover:scale-105 transition-transform">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-red-100 text-lg">Current Streak</p>
                    <p className="text-4xl font-bold">{studentData.streak}</p>
                    <p className="text-red-200 text-sm">Days in a row!</p>
                  </div>
                  <div className="text-6xl">🔥</div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Level Progress */}
          <Card className="mb-8 bg-gradient-to-r from-green-100 to-blue-100 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-2xl">
                <TrendingUp className="h-6 w-6 text-green-600" />
                Level Progress 🚀
              </CardTitle>
              <CardDescription className="text-lg">
                Only {studentData.nextLevelXP - studentData.xp} XP away from Level {studentData.level + 1}! You're so
                close! 🎯
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between text-lg font-semibold">
                  <span className="flex items-center gap-2">
                    <span className="text-2xl">🌟</span>
                    Level {studentData.level}
                  </span>
                  <span className="flex items-center gap-2">
                    Level {studentData.level + 1}
                    <span className="text-2xl">⭐</span>
                  </span>
                </div>
                <div className="relative">
                  <Progress value={progressToNext} className="h-6 bg-gray-200" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-sm font-bold text-white drop-shadow-lg">
                      {Math.round(progressToNext)}% Complete!
                    </span>
                  </div>
                </div>
                <p className="text-center text-lg font-semibold text-gray-700">
                  {studentData.xp} / {studentData.nextLevelXP} XP
                </p>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5 bg-white/80 backdrop-blur-sm shadow-lg">
              <TabsTrigger value="overview" className="text-lg font-semibold">
                🏠 Overview
              </TabsTrigger>
              <TabsTrigger value="badges" className="text-lg font-semibold">
                🏆 Badges
              </TabsTrigger>
              <TabsTrigger value="goals" className="text-lg font-semibold">
                🎯 Goals
              </TabsTrigger>
              <TabsTrigger value="leaderboard" className="text-lg font-semibold">
                👑 Leaderboard
              </TabsTrigger>
              <TabsTrigger value="progress" className="text-lg font-semibold">
                📊 Progress
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-xl">
                      <Zap className="h-6 w-6 text-yellow-500" />
                      Recent Adventures 🌟
                    </CardTitle>
                    <CardDescription>Your latest achievements and activities</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {studentData.recentActivities.map((activity, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border-l-4 border-blue-400 hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-center gap-3">
                            <span className="text-2xl">{activity.emoji}</span>
                            <div>
                              <p className="font-semibold text-gray-800">{activity.activity}</p>
                              <p className="text-sm text-gray-600">{activity.date}</p>
                            </div>
                          </div>
                          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-lg px-3 py-1">
                            +{activity.xp} XP
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-xl">
                      <Target className="h-6 w-6 text-green-500" />
                      Weekly Performance 📈
                    </CardTitle>
                    <CardDescription>How awesome you've been this week!</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-lg font-semibold flex items-center gap-2">
                          <span>✅</span> Attendance
                        </span>
                        <span className="text-lg font-bold text-green-600">
                          {studentData.weeklyProgress.attendance}%
                        </span>
                      </div>
                      <Progress value={studentData.weeklyProgress.attendance} className="h-4" />
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-lg font-semibold flex items-center gap-2">
                          <span>📝</span> Homework
                        </span>
                        <span className="text-lg font-bold text-blue-600">{studentData.weeklyProgress.homework}%</span>
                      </div>
                      <Progress value={studentData.weeklyProgress.homework} className="h-4" />
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-lg font-semibold flex items-center gap-2">
                          <span>😊</span> Behavior
                        </span>
                        <span className="text-lg font-bold text-purple-600">
                          {studentData.weeklyProgress.behavior}%
                        </span>
                      </div>
                      <Progress value={studentData.weeklyProgress.behavior} className="h-4" />
                    </div>

                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-lg font-semibold flex items-center gap-2">
                          <span>🙋‍♀️</span> Participation
                        </span>
                        <span className="text-lg font-bold text-orange-600">
                          {studentData.weeklyProgress.participation}%
                        </span>
                      </div>
                      <Progress value={studentData.weeklyProgress.participation} className="h-4" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="badges">
              <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-2xl">
                    <Trophy className="h-8 w-8 text-yellow-500" />
                    My Badge Collection 🏆
                  </CardTitle>
                  <CardDescription className="text-lg">
                    Amazing badges you've earned for your achievements!
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {studentData.badges.map((badge, index) => (
                      <div
                        key={index}
                        className="border-2 border-yellow-300 rounded-xl p-6 text-center space-y-4 bg-gradient-to-br from-yellow-50 to-orange-50 hover:shadow-lg transition-shadow transform hover:scale-105"
                      >
                        <div className="text-6xl">{badge.icon}</div>
                        <div
                          className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold ${badge.color}`}
                        >
                          {badge.name}
                        </div>
                        <p className="text-sm text-gray-600 font-semibold">Earned on {badge.earned}</p>
                      </div>
                    ))}

                    {/* Locked badges */}
                    {[1, 2, 3, 4].map((_, index) => (
                      <div
                        key={index}
                        className="border-2 border-gray-300 rounded-xl p-6 text-center space-y-4 bg-gray-50 opacity-60"
                      >
                        <div className="text-6xl">🔒</div>
                        <div className="inline-flex items-center px-4 py-2 rounded-full text-lg font-bold bg-gray-200 text-gray-500">
                          Mystery Badge
                        </div>
                        <p className="text-sm text-gray-400 font-semibold">Keep learning to unlock!</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="goals">
              <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-2xl">
                    <Target className="h-8 w-8 text-green-500" />
                    My Learning Goals 🎯
                  </CardTitle>
                  <CardDescription className="text-lg">
                    Track your progress towards your personal goals!
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {studentData.goals.map((goal, index) => (
                      <div
                        key={index}
                        className="border-2 border-green-200 rounded-xl p-6 space-y-4 bg-gradient-to-r from-green-50 to-blue-50"
                      >
                        <div className="flex items-center justify-between">
                          <h3 className="text-xl font-bold flex items-center gap-2">
                            <span className="text-2xl">{goal.emoji}</span>
                            {goal.goal}
                          </h3>
                          <Badge variant="outline" className="text-lg px-3 py-1">
                            <Calendar className="h-4 w-4 mr-1" />
                            {goal.deadline}
                          </Badge>
                        </div>
                        <div>
                          <div className="flex justify-between mb-2">
                            <span className="text-lg font-semibold">Progress</span>
                            <span className="text-lg font-bold text-green-600">{goal.progress}%</span>
                          </div>
                          <div className="relative">
                            <Progress value={goal.progress} className="h-4" />
                            <div className="absolute inset-0 flex items-center justify-center">
                              <span className="text-sm font-bold text-white drop-shadow-lg">{goal.progress}%</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="leaderboard">
              <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-2xl">
                    <Users className="h-8 w-8 text-purple-500" />
                    Class Champions 👑
                  </CardTitle>
                  <CardDescription className="text-lg">See how you rank among your awesome classmates!</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {leaderboard.map((student) => (
                      <div
                        key={student.rank}
                        className={`flex items-center justify-between p-6 rounded-xl transition-all hover:shadow-lg ${
                          student.name === studentData.name
                            ? "bg-gradient-to-r from-blue-100 to-purple-100 border-4 border-blue-300 transform scale-105"
                            : "bg-gradient-to-r from-gray-50 to-white border-2 border-gray-200"
                        }`}
                      >
                        <div className="flex items-center gap-6">
                          <div
                            className={`w-12 h-12 rounded-full flex items-center justify-center text-2xl font-bold ${
                              student.rank === 1
                                ? "bg-gradient-to-r from-yellow-400 to-yellow-600 text-white"
                                : student.rank === 2
                                  ? "bg-gradient-to-r from-gray-400 to-gray-600 text-white"
                                  : student.rank === 3
                                    ? "bg-gradient-to-r from-orange-400 to-orange-600 text-white"
                                    : "bg-gradient-to-r from-blue-400 to-blue-600 text-white"
                            }`}
                          >
                            #{student.rank}
                          </div>
                          <div className="flex items-center gap-3">
                            <span className="text-3xl">{student.avatar}</span>
                            <div>
                              <p className="text-xl font-bold">{student.name}</p>
                              <p className="text-lg text-gray-600">{student.badges} badges earned</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-purple-600">{student.xp} XP</p>
                          {student.rank <= 3 && <p className="text-sm font-semibold text-yellow-600">🏆 Top 3!</p>}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="progress">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-xl">
                      <Flame className="h-6 w-6 text-orange-500" />
                      Monthly Streak 🔥
                    </CardTitle>
                    <CardDescription>Your amazing daily activity streak this month!</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center mb-6">
                      <div className="inline-flex items-center gap-3 text-3xl font-bold text-orange-600 bg-orange-100 px-6 py-3 rounded-full">
                        <Flame className="h-10 w-10" />
                        {studentData.streak} Days
                      </div>
                      <p className="text-lg text-gray-600 mt-2 font-semibold">You're on fire! Keep it up! 🚀</p>
                    </div>

                    <div className="grid grid-cols-7 gap-2">
                      {Array.from({ length: 30 }, (_, i) => (
                        <div
                          key={i}
                          className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all hover:scale-110 ${
                            i < studentData.streak
                              ? "bg-gradient-to-r from-orange-400 to-red-500 text-white border-2 border-orange-300 shadow-lg"
                              : "bg-gray-200 text-gray-400 border-2 border-gray-300"
                          }`}
                        >
                          {i + 1}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white/90 backdrop-blur-sm shadow-xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-xl">
                      <BookOpen className="h-6 w-6 text-blue-500" />
                      Subject Performance 📚
                    </CardTitle>
                    <CardDescription>How you're doing in different subjects!</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {[
                      { subject: "Mathematics", score: 85, color: "from-blue-400 to-blue-600", emoji: "🧮" },
                      { subject: "Science", score: 92, color: "from-green-400 to-green-600", emoji: "🧪" },
                      { subject: "English", score: 78, color: "from-purple-400 to-purple-600", emoji: "📖" },
                      { subject: "Social Studies", score: 88, color: "from-orange-400 to-orange-600", emoji: "🌍" },
                      { subject: "Art", score: 95, color: "from-pink-400 to-pink-600", emoji: "🎨" },
                    ].map((subject, index) => (
                      <div key={index} className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-lg font-semibold flex items-center gap-2">
                            <span className="text-2xl">{subject.emoji}</span>
                            {subject.subject}
                          </span>
                          <span className="text-lg font-bold text-gray-700">{subject.score}%</span>
                        </div>
                        <div className="relative">
                          <div className="w-full bg-gray-200 rounded-full h-4">
                            <div
                              className={`h-4 rounded-full bg-gradient-to-r ${subject.color} transition-all duration-500`}
                              style={{ width: `${subject.score}%` }}
                            ></div>
                          </div>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-sm font-bold text-white drop-shadow-lg">{subject.score}%</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
